# 降水落区文件上传功能说明

## 功能概述

在历史个例考试页面的降水落区绘制页面中，新增了上传其他平台绘制的降水落区图功能。用户可以上传文本格式的降水落区数据文件，系统会自动解析并在地图上显示。

## 支持的文件格式

- **文件类型**: 所有格式（*）
- **文件大小**: 最大10MB
- **推荐格式**: 文本文件（.txt, .dat, .000等）
- **示例文件**: `docs/历史个例落区示意图`

## 文件格式说明

系统支持类似MICAPS格式的降水落区数据文件，文件结构如下：

```
diamond 14 G:\历史个例落区示意图
2025 07 30 21 0
LINES: 0
LINES_SYMBOL: 0
SYMBOLS: 0
CLOSED_CONTOURS: 5
3 55
   112.267    41.012     0.000   111.947    40.942     0.000   ...
   ...
100 1
   112.267    41.012     0.000
3 73
   110.800    40.014     0.000   110.560    40.049     0.000   ...
   ...
50 1
   110.800    40.014     0.000
...
STATION_SITUATION
53362 100
53367 100
...
```

### 关键字段说明

1. **CLOSED_CONTOURS**: 标识闭合轮廓段开始
2. **轮廓开始行**: 格式为 `无用数字 点数`，如 `3 55` 表示3无用，55个坐标点
3. **坐标行**: 每行包含多个坐标点，格式为 `经度 纬度 高度`
4. **结束标识**: 格式为 `降水量 1` + 下一行坐标，如 `100 1` + `112.267 41.012 0.000`
5. **结束标识说明**: `100 1` 表示降水量100，下面一行是位置坐标，在本系统中无用
6. **STATION_SITUATION**: （可选）标识轮廓段结束，如果文件中没有此字段也能正常解析

### 解析示例

以下是一个完整的数据块解析过程：

```
CLOSED_CONTOURS: 1
3 55                          ← 3无用，55个坐标点
   112.267    41.012     0.000 ← 坐标数据（第1个点）
   111.947    40.942     0.000 ← 坐标数据（第2个点）
   ...                        ← 更多坐标数据（共55个点）
   112.266    41.019     0.000 ← 坐标数据（第55个点）
100 1                         ← 降水量100，1个无用坐标点
   112.267    41.012     0.000 ← 无用位置坐标，系统自动跳过
```

**解析规则**：
- `无用数字 点数`：第一个数字无用，第二个数字表示接下来的坐标点数量
- 读取指定数量的坐标点作为轮廓数据
- `降水量 1`：降水量值，1表示下面有1个无用坐标点
- 系统会自动跳过结束标识后的无用坐标点
- 使用降水量值作为该轮廓的量级标识
- 如果文件中没有 `STATION_SITUATION` 字段，解析会在文件结束时自动完成

### 智能量级识别

系统根据降水量数值智能识别降水等级：

**识别规则**：
- **0.1-9.9mm** → level0 (小雨)
- **10.0-24.9mm** → level10 (中雨)
- **25.0-49.9mm** → level25 (大雨)
- **50.0-99.9mm** → level50 (暴雨)
- **100.0-249.9mm** → level100 (大暴雨)
- **≥250.0mm** → level250 (特大暴雨)

**示例**：
- 文件中的 `0.1` → 识别为小雨
- 文件中的 `5.5` → 识别为小雨
- 文件中的 `15.0` → 识别为中雨
- 文件中的 `35.2` → 识别为大雨

**注意**：系统会自动根据数值范围进行分类，无需手动映射固定数值。

## 使用方法

### 1. 进入降水落区绘制页面

1. 登录系统
2. 进入历史个例考试页面
3. 在降水落区绘制区域找到工具栏

### 2. 上传文件

1. 点击 **"上传落区图"** 按钮
2. 选择要上传的降水落区数据文件
3. 系统自动上传并解析文件
4. 解析成功后，降水落区会自动显示在地图上

### 3. 功能特点

- **自动解析**: 系统自动识别文件格式并解析数据
- **实时显示**: 解析完成后立即在地图上显示
- **多量级支持**: 支持多个降水量级的同时显示
- **数据保存**: 上传的数据会自动保存到答题记录中

## 技术实现

### 前端实现

- **组件**: `PrecipitationDrawing.vue`
- **上传组件**: Element UI的 `el-upload`
- **文件解析**: 前端JavaScript解析
- **地图显示**: OpenLayers地图库

### 后端实现

- **接口**: `/exam/api/weather/case/upload/precipitation-area`
- **控制器**: `WeatherFileUploadController.java`
- **文件存储**: `uploads/weather/precipitation-area/`
- **解析引擎**: Java正则表达式解析

### API接口

```javascript
POST /exam/api/weather/case/upload/precipitation-area
Content-Type: multipart/form-data

参数:
- file: 上传的文件

返回:
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1722348123456,
    "name": "历史个例落区示意图",
    "fileName": "generated_filename.txt",
    "size": 12345,
    "url": "/uploads/weather/precipitation-area/generated_filename.txt",
    "content": "文件原始内容",
    "parsedData": {
      "success": true,
      "data": {
        "level100": [...],
        "level50": [...],
        "level25": [...]
      }
    }
  }
}
```

## 注意事项

1. **文件格式**: 确保文件格式符合要求，包含CLOSED_CONTOURS段
2. **坐标系统**: 坐标应为WGS84地理坐标系（经纬度）
3. **文件大小**: 单个文件不超过10MB
4. **数据覆盖**: 上传新文件会覆盖当前绘制的数据
5. **权限要求**: 需要登录用户权限

## 错误处理

- **文件为空**: 提示"上传文件不能为空"
- **文件过大**: 提示"文件大小不能超过10MB"
- **解析失败**: 提示具体的解析错误信息
- **网络错误**: 提示"文件上传失败，请重试"

## 示例文件

系统提供了示例文件 `docs/历史个例落区示意图`，包含完整的降水落区数据格式，可作为参考。

## 更新日志

- **2025-07-30**: 初始版本发布
  - 支持文本格式降水落区文件上传
  - 自动解析MICAPS格式数据
  - 实时地图显示功能
  - 多量级降水数据支持
